#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DFM模型训练测试脚本
模拟UI界面的参数设置，测试完整的数据准备和模型训练流程
"""

import os
import sys
import pandas as pd
from datetime import datetime, date
import traceback

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
dashboard_dir = os.path.join(current_dir, 'dashboard')
sys.path.insert(0, dashboard_dir)

def test_dfm_training():
    """测试DFM训练流程"""
    print("=" * 60)
    print("🚀 开始DFM模型训练测试")
    print("=" * 60)
    
    # 1. 检查原始数据文件
    excel_file_path = os.path.join(current_dir, 'data', '经济数据库0605.xlsx')
    print(f"\n📁 检查数据文件: {excel_file_path}")
    
    if not os.path.exists(excel_file_path):
        print(f"❌ 数据文件不存在: {excel_file_path}")
        return False
    
    print(f"✅ 数据文件存在，大小: {os.path.getsize(excel_file_path) / 1024 / 1024:.2f} MB")
    
    # 2. 检查Excel文件结构
    try:
        xl_file = pd.ExcelFile(excel_file_path)
        sheet_names = xl_file.sheet_names
        print(f"📊 Excel文件包含 {len(sheet_names)} 个工作表")
        
        # 显示前10个工作表
        print("前10个工作表:")
        for i, name in enumerate(sheet_names[:10]):
            print(f"  {i+1}. {name}")
        
        if len(sheet_names) > 10:
            print(f"  ... 还有 {len(sheet_names) - 10} 个工作表")
            
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        return False
    
    # 3. 导入数据准备模块
    print(f"\n🔧 导入数据准备模块...")
    try:
        from DFM.data_prep.data_preparation import prepare_data, load_mappings
        print("✅ 数据准备模块导入成功")
    except ImportError as e:
        print(f"❌ 数据准备模块导入失败: {e}")
        return False
    
    # 4. 模拟截图中的参数设置
    print(f"\n⚙️ 设置训练参数（基于截图）...")
    
    # 基于截图的参数设置
    params = {
        # 数据准备参数
        'target_freq': 'W-FRI',  # 周频率，周五
        'target_sheet_name': '规模以上工业增加值:当月同比',  # 目标工作表
        'target_variable_name': '规模以上工业增加值:当月同比',  # 目标变量
        'consecutive_nan_threshold': 10,  # 连续NaN阈值
        'data_start_date': '2020-01-01',  # 数据开始日期
        'data_end_date': '2024-12-31',  # 数据结束日期
        'reference_sheet_name': '指标体系',  # 指标映射表
        'reference_column_name': '高频指标',  # 参考列名
        
        # 训练参数（基于截图）
        'training_start_date': '2020-01-03',  # 训练开始日期
        'validation_start_date': '2024-01-05',  # 验证开始日期
        'validation_end_date': '2024-12-31',  # 验证结束日期
        'n_factors': 3,  # 因子数量
        'em_max_iter': 50,  # EM最大迭代次数
        
        # 选择的指标（模拟用户选择）
        'selected_indicators': [
            '制造业PMI',
            '制造业PMI:生产',
            '制造业PMI:新订单',
            '制造业PMI:新出口订单'
        ]
    }
    
    print("参数设置:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    # 5. 执行数据准备
    print(f"\n📊 开始数据准备...")
    try:
        prepared_data, industry_map, transform_log, removed_vars_log = prepare_data(
            excel_path=excel_file_path,
            target_freq=params['target_freq'],
            target_sheet_name=params['target_sheet_name'],
            target_variable_name=params['target_variable_name'],
            consecutive_nan_threshold=params['consecutive_nan_threshold'],
            data_start_date=params['data_start_date'],
            data_end_date=params['data_end_date'],
            reference_sheet_name=params['reference_sheet_name'],
            reference_column_name=params['reference_column_name']
        )
        
        if prepared_data is not None and not prepared_data.empty:
            print(f"✅ 数据准备成功!")
            print(f"   数据形状: {prepared_data.shape}")
            print(f"   时间范围: {prepared_data.index.min()} 到 {prepared_data.index.max()}")
            print(f"   变量数量: {len(prepared_data.columns)}")
            print(f"   前5个变量: {list(prepared_data.columns[:5])}")
            
            # 检查目标变量是否存在
            target_var = params['target_variable_name']
            if target_var in prepared_data.columns:
                print(f"✅ 目标变量存在: {target_var}")
            else:
                print(f"❌ 目标变量不存在: {target_var}")
                print(f"   可用变量: {list(prepared_data.columns)}")
                
        else:
            print(f"❌ 数据准备失败，返回空数据")
            return False
            
    except Exception as e:
        print(f"❌ 数据准备过程出错: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False
    
    # 6. 加载变量映射
    print(f"\n🗺️ 加载变量映射...")
    try:
        var_type_map, var_industry_map = load_mappings(
            excel_path=excel_file_path,
            sheet_name=params['reference_sheet_name'],
            indicator_col=params['reference_column_name'],
            type_col='类型',
            industry_col='行业'
        )
        print(f"✅ 变量映射加载成功")
        print(f"   类型映射: {len(var_type_map)} 个")
        print(f"   行业映射: {len(var_industry_map)} 个")
        
    except Exception as e:
        print(f"⚠️ 变量映射加载失败: {e}")
        var_type_map = {}
        var_industry_map = {}
    
    # 7. 筛选可用的预测指标
    print(f"\n🎯 筛选预测指标...")
    available_indicators = []
    target_var = params['target_variable_name']
    
    for indicator in params['selected_indicators']:
        if indicator in prepared_data.columns and indicator != target_var:
            available_indicators.append(indicator)
            print(f"✅ {indicator}")
        else:
            print(f"❌ {indicator} (不在数据中)")
    
    if not available_indicators:
        print(f"⚠️ 没有可用的预测指标，使用除目标变量外的前5个变量")
        all_vars = [col for col in prepared_data.columns if col != target_var]
        available_indicators = all_vars[:5]
        print(f"自动选择的指标: {available_indicators}")
    
    print(f"最终选择的预测指标数量: {len(available_indicators)}")

    # 8. 导入训练模块
    print(f"\n🤖 导入训练模块...")
    try:
        from DFM.train_model.tune_dfm import train_and_save_dfm_results
        print("✅ 训练模块导入成功")
    except ImportError as e:
        print(f"❌ 训练模块导入失败: {e}")
        return False

    # 9. 执行模型训练
    print(f"\n🚀 开始模型训练...")
    try:
        # 转换日期格式
        training_start = datetime.strptime(params['training_start_date'], '%Y-%m-%d').date()
        validation_start = datetime.strptime(params['validation_start_date'], '%Y-%m-%d').date()
        validation_end = datetime.strptime(params['validation_end_date'], '%Y-%m-%d').date()

        # 调用训练函数
        training_results = train_and_save_dfm_results(
            input_df=prepared_data,
            target_variable=params['target_variable_name'],
            selected_indicators=available_indicators,
            training_start_date=training_start,
            training_end_date=None,  # 自动计算
            validation_start_date=validation_start,
            validation_end_date=validation_end,
            n_factors=params['n_factors'],
            em_max_iter=params['em_max_iter'],
            output_base_dir="test_outputs",
            # 映射参数
            var_industry_map=var_industry_map,
            var_type_map=var_type_map,
            # 其他参数
            enable_hyperparameter_tuning=False,
            enable_variable_selection=False,
            enable_detailed_analysis=True,
            generate_excel_report=True,
            # 完整数据准备参数
            excel_file_path=excel_file_path,
            use_full_data_preparation=False  # 已经准备好数据了
        )

        if training_results:
            print(f"✅ 模型训练成功!")
            print(f"生成的文件:")
            for file_type, file_path in training_results.items():
                if file_path and os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"  ✅ {file_type}: {file_path} ({file_size} bytes)")
                else:
                    print(f"  ❌ {file_type}: {file_path} (文件不存在)")
        else:
            print(f"❌ 模型训练失败，未返回结果")
            return False

    except Exception as e:
        print(f"❌ 模型训练过程出错: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

    # 10. 验证结果
    print(f"\n✅ 验证训练结果...")

    # 检查输出目录
    output_dir = "test_outputs"
    if os.path.exists(output_dir):
        files = os.listdir(output_dir)
        print(f"输出目录包含 {len(files)} 个文件:")
        for f in files:
            file_path = os.path.join(output_dir, f)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  📄 {f} ({size} bytes)")
    else:
        print(f"❌ 输出目录不存在: {output_dir}")

    return True

if __name__ == "__main__":
    success = test_dfm_training()
    if success:
        print(f"\n🎉 DFM模型训练测试完成!")
        print(f"✅ 所有步骤都成功执行")
    else:
        print(f"\n❌ 测试失败!")
        print(f"请检查错误信息并修复问题")
