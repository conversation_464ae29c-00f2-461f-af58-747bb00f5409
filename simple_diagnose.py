#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化诊断脚本 - 避免编码问题
"""

import os
import sys
import pandas as pd
import traceback

def diagnose():
    """诊断问题"""
    print("开始诊断...")
    
    # 1. 检查环境
    print("当前工作目录:", os.getcwd())
    print("Python版本:", sys.version.split()[0])
    
    # 2. 检查文件
    excel_file = 'data/经济数据库0605.xlsx'
    print("\n检查数据文件:", excel_file)
    print("文件存在:", os.path.exists(excel_file))
    
    if os.path.exists(excel_file):
        try:
            xl_file = pd.ExcelFile(excel_file)
            sheet_names = xl_file.sheet_names
            print("工作表数量:", len(sheet_names))
            
            target_sheet = '规模以上工业增加值:当月同比'
            print("目标工作表存在:", target_sheet in sheet_names)
            
            indicators_sheet = '指标体系'
            print("指标体系工作表存在:", indicators_sheet in sheet_names)
            
            if target_sheet in sheet_names:
                # 尝试读取目标工作表
                df = pd.read_excel(excel_file, sheet_name=target_sheet, nrows=3)
                print("目标工作表可读取: True")
                print("目标工作表形状:", df.shape)
                
        except Exception as e:
            print("读取Excel文件出错:", str(e))
    
    # 3. 检查模块导入
    print("\n检查模块导入...")
    
    # 添加dashboard路径
    dashboard_path = os.path.join(os.getcwd(), 'dashboard')
    if dashboard_path not in sys.path:
        sys.path.insert(0, dashboard_path)
    print("添加路径:", dashboard_path)
    
    try:
        from DFM.data_prep.data_preparation import prepare_data
        print("data_preparation模块导入: 成功")
    except ImportError as e:
        print("data_preparation模块导入: 失败")
        print("错误:", str(e))
    
    try:
        from DFM.train_model.tune_dfm import train_and_save_dfm_results
        print("tune_dfm模块导入: 成功")
    except ImportError as e:
        print("tune_dfm模块导入: 失败")
        print("错误:", str(e))
    
    # 4. 尝试简单的数据准备测试
    print("\n尝试简单的数据准备测试...")
    try:
        from DFM.data_prep.data_preparation import prepare_data
        
        # 最简单的参数
        result = prepare_data(
            excel_path=excel_file,
            target_freq='W-FRI',
            target_sheet_name='规模以上工业增加值:当月同比',
            target_variable_name='规模以上工业增加值:当月同比'
        )
        
        if result and len(result) == 4:
            prepared_data, industry_map, transform_log, removed_vars_log = result
            if prepared_data is not None:
                print("简单数据准备: 成功")
                print("数据形状:", prepared_data.shape)
                print("变量数量:", len(prepared_data.columns))
                
                # 保存数据用于检查
                prepared_data.to_csv('simple_test_data.csv', encoding='utf-8-sig')
                print("数据已保存到 simple_test_data.csv")
                
                # 检查变量
                target_var = '规模以上工业增加值:当月同比'
                predictor_vars = [col for col in prepared_data.columns if col != target_var]
                print("目标变量存在:", target_var in prepared_data.columns)
                print("预测变量数量:", len(predictor_vars))
                
                if len(predictor_vars) > 0:
                    print("前5个预测变量:", predictor_vars[:5])
                    print("可以进行DFM训练: True")
                else:
                    print("可以进行DFM训练: False - 没有预测变量")
                    
            else:
                print("简单数据准备: 失败 - 返回None")
        else:
            print("简单数据准备: 失败 - 返回格式错误")
            
    except Exception as e:
        print("简单数据准备: 失败")
        print("错误:", str(e))
        print("详细错误:")
        print(traceback.format_exc())

if __name__ == "__main__":
    diagnose()
    
    # 保存诊断结果
    with open('simple_diagnosis_result.txt', 'w', encoding='utf-8') as f:
        f.write("诊断完成\n")
    
    print("\n诊断完成！")
