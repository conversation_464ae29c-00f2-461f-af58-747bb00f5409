#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DFM模型完整测试脚本
基于截图参数进行测试
"""

import os
import sys
import pandas as pd
from datetime import datetime, date
import traceback
import io

def log_message(message):
    """记录日志消息"""
    print(message)
    with open('test_log.txt', 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {message}\n")

def main():
    """主测试函数"""
    log_message("=" * 60)
    log_message("🚀 开始DFM模型完整测试")
    log_message("=" * 60)
    
    try:
        # 1. 环境检查
        log_message(f"当前工作目录: {os.getcwd()}")
        log_message(f"Python版本: {sys.version}")
        
        # 2. 文件检查
        excel_file = 'data/经济数据库0605.xlsx'
        log_message(f"检查数据文件: {excel_file}")
        
        if not os.path.exists(excel_file):
            log_message(f"❌ 数据文件不存在: {excel_file}")
            return False
        
        file_size = os.path.getsize(excel_file) / 1024 / 1024
        log_message(f"✅ 数据文件存在，大小: {file_size:.2f} MB")
        
        # 3. 读取Excel文件结构
        log_message("📊 分析Excel文件结构...")
        xl_file = pd.ExcelFile(excel_file)
        sheet_names = xl_file.sheet_names
        log_message(f"工作表数量: {len(sheet_names)}")
        
        # 检查关键工作表
        target_sheet = '规模以上工业增加值:当月同比'
        indicators_sheet = '指标体系'
        
        if target_sheet in sheet_names:
            log_message(f"✅ 找到目标工作表: {target_sheet}")
        else:
            log_message(f"❌ 未找到目标工作表: {target_sheet}")
            log_message("可用工作表前10个:")
            for i, name in enumerate(sheet_names[:10]):
                log_message(f"  {i+1}. {name}")
        
        if indicators_sheet in sheet_names:
            log_message(f"✅ 找到指标体系工作表: {indicators_sheet}")
        else:
            log_message(f"❌ 未找到指标体系工作表: {indicators_sheet}")
        
        # 4. 设置Python路径
        dashboard_path = os.path.join(os.getcwd(), 'dashboard')
        if dashboard_path not in sys.path:
            sys.path.insert(0, dashboard_path)
        log_message(f"添加路径: {dashboard_path}")
        
        # 5. 导入模块
        log_message("🔧 导入必要模块...")
        
        try:
            from DFM.data_prep.data_preparation import prepare_data, load_mappings
            log_message("✅ 数据准备模块导入成功")
        except ImportError as e:
            log_message(f"❌ 数据准备模块导入失败: {e}")
            return False
        
        try:
            from DFM.train_model.tune_dfm import train_and_save_dfm_results
            log_message("✅ 训练模块导入成功")
        except ImportError as e:
            log_message(f"❌ 训练模块导入失败: {e}")
            return False
        
        # 6. 执行数据准备
        log_message("📊 开始数据准备...")
        
        # 基于截图的参数
        params = {
            'target_freq': 'W-FRI',
            'target_sheet_name': target_sheet,
            'target_variable_name': target_sheet,
            'consecutive_nan_threshold': 10,
            'data_start_date': '2020-01-01',
            'data_end_date': '2024-12-31',
            'reference_sheet_name': indicators_sheet,
            'reference_column_name': '高频指标'
        }
        
        log_message("参数设置:")
        for key, value in params.items():
            log_message(f"  {key}: {value}")
        
        # 执行数据准备
        prepared_data, industry_map, transform_log, removed_vars_log = prepare_data(
            excel_path=excel_file,
            target_freq=params['target_freq'],
            target_sheet_name=params['target_sheet_name'],
            target_variable_name=params['target_variable_name'],
            consecutive_nan_threshold=params['consecutive_nan_threshold'],
            data_start_date=params['data_start_date'],
            data_end_date=params['data_end_date'],
            reference_sheet_name=params['reference_sheet_name'],
            reference_column_name=params['reference_column_name']
        )
        
        if prepared_data is not None and not prepared_data.empty:
            log_message(f"✅ 数据准备成功!")
            log_message(f"   数据形状: {prepared_data.shape}")
            log_message(f"   时间范围: {prepared_data.index.min()} 到 {prepared_data.index.max()}")
            log_message(f"   变量数量: {len(prepared_data.columns)}")
            
            # 保存准备好的数据用于检查
            prepared_data.to_csv('test_prepared_data.csv', encoding='utf-8-sig')
            log_message("✅ 准备好的数据已保存到 test_prepared_data.csv")
            
            # 显示前几个变量
            log_message(f"前10个变量: {list(prepared_data.columns[:10])}")
            
        else:
            log_message(f"❌ 数据准备失败")
            return False
        
        # 7. 选择预测变量
        log_message("🎯 选择预测变量...")
        target_var = params['target_variable_name']
        
        # 基于截图选择一些常见的经济指标
        candidate_indicators = [
            '制造业PMI',
            '制造业PMI:生产',
            '制造业PMI:新订单',
            '制造业PMI:新出口订单',
            'CPI:当月同比',
            'PPI:当月同比',
            '社会消费品零售总额:当月同比',
            '固定资产投资完成额:累计同比'
        ]
        
        selected_indicators = []
        for indicator in candidate_indicators:
            if indicator in prepared_data.columns and indicator != target_var:
                selected_indicators.append(indicator)
                log_message(f"✅ 选择指标: {indicator}")
        
        # 如果没有找到预定义的指标，选择前几个可用的
        if not selected_indicators:
            all_vars = [col for col in prepared_data.columns if col != target_var]
            selected_indicators = all_vars[:5]
            log_message(f"⚠️ 使用前5个可用变量: {selected_indicators}")
        
        log_message(f"最终选择的预测指标数量: {len(selected_indicators)}")
        
        if len(selected_indicators) == 0:
            log_message(f"❌ 没有可用的预测指标")
            return False
        
        return True
        
    except Exception as e:
        log_message(f"❌ 测试过程出错: {e}")
        log_message(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    # 清空日志文件
    with open('test_log.txt', 'w', encoding='utf-8') as f:
        f.write("")
    
    success = main()
    
    if success:
        log_message("\n🎉 测试成功完成!")
    else:
        log_message("\n❌ 测试失败!")
    
    log_message("详细日志已保存到 test_log.txt")
