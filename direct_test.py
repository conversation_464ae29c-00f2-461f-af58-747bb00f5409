#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import traceback

# 添加dashboard路径
dashboard_path = os.path.join(os.getcwd(), 'dashboard')
sys.path.insert(0, dashboard_path)

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from DFM.data_prep.data_preparation import prepare_data
        print("✓ data_preparation导入成功")
        return True
    except Exception as e:
        print(f"✗ data_preparation导入失败: {e}")
        print(traceback.format_exc())
        return False

def test_data_file():
    """测试数据文件"""
    print("\n测试数据文件...")
    
    excel_file = 'data/经济数据库0605.xlsx'
    if os.path.exists(excel_file):
        print(f"✓ 数据文件存在: {excel_file}")
        
        try:
            import pandas as pd
            xl_file = pd.ExcelFile(excel_file)
            print(f"✓ 可以读取Excel文件，包含 {len(xl_file.sheet_names)} 个工作表")
            
            # 检查关键工作表
            target_sheet = '规模以上工业增加值:当月同比'
            if target_sheet in xl_file.sheet_names:
                print(f"✓ 找到目标工作表: {target_sheet}")
            else:
                print(f"✗ 未找到目标工作表: {target_sheet}")
                print(f"可用工作表: {xl_file.sheet_names[:5]}...")
            
            return True
        except Exception as e:
            print(f"✗ 读取Excel文件失败: {e}")
            return False
    else:
        print(f"✗ 数据文件不存在: {excel_file}")
        return False

def test_data_preparation():
    """测试数据准备"""
    print("\n测试数据准备...")
    
    try:
        from DFM.data_prep.data_preparation import prepare_data
        
        excel_file = 'data/经济数据库0605.xlsx'
        
        # 使用最基本的参数
        result = prepare_data(
            excel_path=excel_file,
            target_freq='W-FRI',
            target_sheet_name='规模以上工业增加值:当月同比',
            target_variable_name='规模以上工业增加值:当月同比'
        )
        
        if result and len(result) == 4:
            prepared_data, industry_map, transform_log, removed_vars_log = result
            
            if prepared_data is not None and not prepared_data.empty:
                print(f"✓ 数据准备成功")
                print(f"  数据形状: {prepared_data.shape}")
                print(f"  变量数量: {len(prepared_data.columns)}")
                print(f"  变量列表: {list(prepared_data.columns)}")
                
                # 保存数据用于检查
                prepared_data.to_csv('direct_test_data.csv', encoding='utf-8-sig')
                print(f"✓ 数据已保存到 direct_test_data.csv")
                
                # 检查是否有预测变量
                target_var = '规模以上工业增加值:当月同比'
                predictor_vars = [col for col in prepared_data.columns if col != target_var]
                
                if len(predictor_vars) > 0:
                    print(f"✓ 找到 {len(predictor_vars)} 个预测变量")
                    print(f"  前5个预测变量: {predictor_vars[:5]}")
                    return True, prepared_data, industry_map
                else:
                    print(f"✗ 没有预测变量，只有目标变量")
                    return False, None, None
            else:
                print(f"✗ 数据准备返回空数据")
                return False, None, None
        else:
            print(f"✗ 数据准备返回格式错误")
            return False, None, None
            
    except Exception as e:
        print(f"✗ 数据准备失败: {e}")
        print(traceback.format_exc())
        return False, None, None

def test_training(prepared_data, industry_map):
    """测试模型训练"""
    print("\n测试模型训练...")
    
    try:
        from DFM.train_model.tune_dfm import train_and_save_dfm_results
        from datetime import date
        
        # 选择预测变量
        target_var = '规模以上工业增加值:当月同比'
        predictor_vars = [col for col in prepared_data.columns if col != target_var]
        selected_indicators = predictor_vars[:3]  # 选择前3个
        
        print(f"  目标变量: {target_var}")
        print(f"  选择的预测变量: {selected_indicators}")
        
        # 运行训练
        results = train_and_save_dfm_results(
            input_df=prepared_data,
            target_variable=target_var,
            selected_indicators=selected_indicators,
            training_start_date=date(2020, 1, 3),
            validation_start_date=date(2024, 1, 5),
            validation_end_date=date(2024, 12, 31),
            n_factors=3,
            em_max_iter=50,
            output_base_dir="direct_test_outputs",
            var_industry_map=industry_map,
            enable_detailed_analysis=True,
            generate_excel_report=True
        )
        
        if results:
            print(f"✓ 模型训练成功")
            print(f"  生成的文件:")
            for file_type, file_path in results.items():
                if file_path and os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    print(f"    {file_type}: {file_path} ({size} bytes)")
                else:
                    print(f"    {file_type}: {file_path} (文件不存在)")
            return True
        else:
            print(f"✗ 模型训练失败")
            return False
            
    except Exception as e:
        print(f"✗ 模型训练失败: {e}")
        print(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("DFM模型直接测试")
    print("=" * 50)
    
    results = {}
    
    # 1. 测试导入
    results['imports'] = test_imports()
    
    # 2. 测试数据文件
    results['data_file'] = test_data_file()
    
    # 3. 测试数据准备
    if results['imports'] and results['data_file']:
        success, prepared_data, industry_map = test_data_preparation()
        results['data_preparation'] = success
        
        # 4. 测试训练
        if success and prepared_data is not None:
            results['training'] = test_training(prepared_data, industry_map)
        else:
            results['training'] = False
    else:
        results['data_preparation'] = False
        results['training'] = False
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    for test_name, success in results.items():
        status = "✓ 成功" if success else "✗ 失败"
        print(f"{test_name}: {status}")
    
    all_success = all(results.values())
    print(f"\n总体结果: {'✓ 全部成功' if all_success else '✗ 部分失败'}")
    
    # 保存结果
    with open('direct_test_results.txt', 'w', encoding='utf-8') as f:
        f.write("DFM模型直接测试结果\n")
        f.write("=" * 30 + "\n")
        for test_name, success in results.items():
            f.write(f"{test_name}: {'成功' if success else '失败'}\n")
        f.write(f"\n总体结果: {'全部成功' if all_success else '部分失败'}\n")
    
    return all_success

if __name__ == "__main__":
    success = main()
    print(f"\n测试完成，结果已保存到 direct_test_results.txt")
