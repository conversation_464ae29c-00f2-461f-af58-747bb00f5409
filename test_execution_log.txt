DFM测试执行日志 - 开始时间: 2025-06-16 09:54:47.029140
============================================================
2025-06-16 09:54:47.029895: ============================================================
2025-06-16 09:54:47.030427: 🚀 DFM模型完整测试 - 基于截图参数
2025-06-16 09:54:47.031041: ============================================================
2025-06-16 09:54:47.031557: 
1️⃣ 环境设置...
2025-06-16 09:54:47.032079: ✅ 添加路径: C:\Users\<USER>\Desktop\HFTA0610_test\dashboard
2025-06-16 09:54:47.032638: 
2️⃣ 检查数据文件...
2025-06-16 09:54:47.033452: 📁 检查数据文件: data/经济数据库0605.xlsx
2025-06-16 09:54:47.034273: ✅ 数据文件存在，大小: 0.48 MB
2025-06-16 09:54:47.262229: 📊 Excel文件包含 15 个工作表
2025-06-16 09:54:47.263010: ✅ 找到目标工作表: 工业增加值同比增速_月度_同花顺
2025-06-16 09:54:47.263472: ✅ 找到指标体系工作表: 指标体系
2025-06-16 09:54:47.264268: 
3️⃣ 测试数据准备...
2025-06-16 09:54:47.264979: 
🔧 测试数据准备模块...
2025-06-16 09:54:48.108774: ✅ 数据准备模块导入成功
2025-06-16 09:54:48.109701: 📊 开始数据准备，参数设置:
2025-06-16 09:54:48.110464:   target_freq: W-FRI
2025-06-16 09:54:48.111099:   target_sheet_name: 工业增加值同比增速_月度_同花顺
2025-06-16 09:54:48.111621:   target_variable_name: 工业增加值同比增速_月度_同花顺
2025-06-16 09:54:48.112124:   consecutive_nan_threshold: 10
2025-06-16 09:54:48.112636:   data_start_date: 2020-01-01
2025-06-16 09:54:48.113122:   data_end_date: 2024-12-31
2025-06-16 09:54:48.113398:   reference_sheet_name: 指标体系
2025-06-16 09:54:48.113657:   reference_column_name: 高频指标
2025-06-16 09:54:49.317852: ✅ 数据准备成功!
2025-06-16 09:54:49.318483:    数据形状: (262, 76)
2025-06-16 09:54:49.319230:    时间范围: 2019-12-27 00:00:00 到 2024-12-27 00:00:00
2025-06-16 09:54:49.319700:    变量数量: 76
2025-06-16 09:54:49.331101: ✅ 准备好的数据已保存到 test_prepared_data.csv
2025-06-16 09:54:49.331681: 变量列表: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石', '中国:生产率:焦炉:国内独立焦化厂(230家)', '中国:高炉开工率(247家)', '中国:产量:螺纹钢:主要钢厂', '中国:开工率:线材:主要钢厂', '中国:产量:线材:主要钢厂', '中国:产量:冷轧板卷:主要钢厂', '中国:产量:热轧板卷:主要钢厂', '中国:产量:中厚板:主要钢厂', '中国:产量:钢材:重点钢铁企业', '中国:开工率:产能(>200万吨):焦化企业(230家)', '中国:开工率:产能(100-200万吨):焦化企业(230家)', '中国:主流港口:库存量:煤炭', '中国:库存量:焦炭:国内样本钢厂(247家)', '动力煤：462家样本矿山：产能利用率（周）', '动力煤：462家样本矿山：日均产量（周）', '精煤：样本洗煤厂（110家）：日均产量：中国（周）', '煤炭：样本洗煤厂（110家）：开工率：中国（周）', '精煤：523家样本矿山：日均产量（周）', '原煤：523家样本矿山：日均产量（周）', '炼焦煤：523家样本矿山：开工率（周）', '焦炭：230家独立焦化厂：产能利用率：中国（周）', '焦炭：230家独立焦化厂：日均产量：中国（周）', '中国:开工率(常减压开工率):山东地炼厂', '中国:产能利用率:成品油:独立炼厂', '中国:产能利用率:成品油:主营炼厂', '乙烯：MTO：生产企业：产能利用率：中国（周）', '乙烯：轻烃裂解：生产企业：产能利用率：中国（周）', '乙烯：石脑油裂解：生产企业：产能利用率：中国（周）', '乙烯：MTO：产量：中国（周）', '乙烯：产量：中国（周）', '乙烯：轻烃裂解：产量：中国（周）', '乙烯：石脑油裂解：产量：中国（周）', '乙烯：市场价：华东地区（周）', '氯化铵：产量：中国（周）', '氯化铵：产能利用率：中国（周）', '三聚氰胺：产量：中国（周）', '三聚氰胺：产能利用率：中国（周）', '尿素：产量：中国（周）', '尿素：产能利用率：中国（周）', '磷酸一铵：工业级：产能利用率：中国（周）', '磷酸一铵：工业级：产量：中国（周）', '磷酸一铵：产量：中国（周）', '磷酸二铵：产量：中国（周）', '硫酸钾：开工率：中国（周）', '氯化钾：产能利用率：中国（周）', '中国:开工率:精对苯二甲酸', '中国:江浙地区:开工率:涤纶长丝', '中国:装置负荷率:涤纶短纤', 'PTA：产能利用率：中国（周）', 'PTA：产量：中国（周）', 'MEG：产能利用率：中国（周）', 'MEG：产量：中国（周）', '聚酯：产能利用率：中国（周）', '聚酯：产量：中国（周）', 'PE：产能利用率：中国（周）', 'PE：化工生产企业：产量：中国（周）', 'PE：社会库存：中国（周）', 'PP：注塑：开工率：中国（周）', 'PP：产能利用率：中国（周）', 'PP：产量：中国（周）', 'PVC：产能利用率：中国（周）', 'PVC：产量：中国（周）', '中国:开工率:汽车轮胎(半钢胎)', '中国:开工率:汽车轮胎(全钢胎)', '制造业PMI', '制造业PMI:生产', '制造业PMI:新订单', '制造业PMI:新出口订单', '制造业PMI:从业人员', '中国：可再生能源：发电量（月）', '中国：火力发电：发电量（月）']
2025-06-16 09:54:49.332070: 
4️⃣ 测试模型训练...
2025-06-16 09:54:49.332516: 
🤖 测试模型训练模块...
2025-06-16 09:54:50.602852: ✅ 训练模块导入成功
2025-06-16 09:54:50.603787: 🎯 变量选择:
2025-06-16 09:54:50.604405:    目标变量: 工业增加值同比增速_月度_同花顺
2025-06-16 09:54:50.605004:    可用预测变量数量: 76
2025-06-16 09:54:50.605602:    选择的预测变量: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石']
2025-06-16 09:54:50.606185: 🚀 开始模型训练，参数:
2025-06-16 09:54:50.606812:    target_variable: 工业增加值同比增速_月度_同花顺
2025-06-16 09:54:50.607415:    selected_indicators: ['规模以上工业增加值:当月同比', '中国:日均产量:生铁:重点企业', '中国:日均产量:粗钢:重点企业', '重点电厂:日耗量:煤炭', '中国:主要45港口:日均疏港量:铁矿石']
2025-06-16 09:54:50.607881:    training_start_date: 2020-01-03
2025-06-16 09:54:50.609350:    validation_start_date: 2024-01-05
2025-06-16 09:54:50.610165:    validation_end_date: 2024-12-31
2025-06-16 09:54:50.610787:    n_factors: 3
2025-06-16 09:54:50.611581:    em_max_iter: 50
2025-06-16 09:54:50.612046:    output_base_dir: test_outputs
2025-06-16 09:54:50.612709:    var_industry_map: {'氯化铵:产量:中国(周)': '化学化工', '磷酸二铵:产量:中国(周)': '化学化工', '乙烯:mto:生产企业:产能利用率:中国(周)': '化学化工', '中国:开工率:汽车轮胎(半钢胎)': '汽车', 'pe:化工生产企业:产量:中国(周)': '橡胶塑料', '中国:日均产量:粗钢:重点企业': '钢铁', '规模以上工业增加值:当月同比': '工业增加值同比增速', '中国:高炉开工率(247家)': '钢铁', '动力煤:462家样本矿山:日均产量(周)': '煤炭', '乙烯:市场价:华东地区(周)': '化学化工', '动力煤:462家样本矿山:产能利用率(周)': '煤炭', '中国:开工率:产能(100-200万吨):焦化企业(230家)': '煤炭', '乙烯:mto:产量:中国(周)': '化学化工', '中国:主流港口:库存量:煤炭': '煤炭', '焦炭:230家独立焦化厂:日均产量:中国(周)': '煤炭', '中国:产量:热轧板卷:主要钢厂': '钢铁', '磷酸一铵:产量:中国(周)': '化学化工', '磷酸一铵:工业级:产能利用率:中国(周)': '化学化工', '乙烯:石脑油裂解:生产企业:产能利用率:中国(周)': '化学化工', '聚酯:产量:中国(周)': '化纤', 'meg:产量:中国(周)': '化纤', 'pp:产能利用率:中国(周)': '橡胶塑料', 'meg:产能利用率:中国(周)': '化纤', '三聚氰胺:产能利用率:中国(周)': '化学化工', '中国:库存量:焦炭:国内样本钢厂(247家)': '煤炭', '中国:主要45港口:日均疏港量:铁矿石': '钢铁', '中国:产能利用率:成品油:主营炼厂': '油气', '炼焦煤:523家样本矿山:开工率(周)': '煤炭', 'pvc:产量:中国(周)': '橡胶塑料', '中国:产量:螺纹钢:主要钢厂': '钢铁', '乙烯:轻烃裂解:生产企业:产能利用率:中国(周)': '化学化工', 'pp:注塑:开工率:中国(周)': '橡胶塑料', '乙烯:轻烃裂解:产量:中国(周)': '化学化工', '中国:开工率:精对苯二甲酸': '化纤', '中国:产能利用率:成品油:独立炼厂': '油气', 'pp:产量:中国(周)': '橡胶塑料', '中国:生产率:焦炉:国内独立焦化厂(230家)': '钢铁', '磷酸一铵:工业级:产量:中国(周)': '化学化工', '氯化钾:产能利用率:中国(周)': '化学化工', '中国:江浙地区:开工率:涤纶长丝': '化纤', '精煤:523家样本矿山:日均产量(周)': '煤炭', '焦炭:230家独立焦化厂:产能利用率:中国(周)': '煤炭', '中国:装置负荷率:涤纶短纤': '化纤', '中国:产量:钢材:重点钢铁企业': '钢铁', '中国:开工率(常减压开工率):山东地炼厂': '油气', '中国:产量:中厚板:主要钢厂': '钢铁', '原煤:523家样本矿山:日均产量(周)': '煤炭', 'pta:产量:中国(周)': '化纤', 'pvc:产能利用率:中国(周)': '橡胶塑料', '中国:开工率:产能(>200万吨):焦化企业(230家)': '煤炭', '制造业pmi:生产': 'PMI', '中国:日均产量:生铁:重点企业': '钢铁', '精煤:样本洗煤厂(110家):日均产量:中国(周)': '煤炭', '中国:开工率:线材:主要钢厂': '钢铁', 'pe:产能利用率:中国(周)': '橡胶塑料', '制造业pmi': 'PMI', '中国:可再生能源:发电量(月)': '电力', '乙烯:产量:中国(周)': '化学化工', '氯化铵:产能利用率:中国(周)': '化学化工', '尿素:产量:中国(周)': '化学化工', '尿素:产能利用率:中国(周)': '化学化工', '中国:开工率:汽车轮胎(全钢胎)': '汽车', '制造业pmi:新订单': 'PMI', '制造业pmi:新出口订单': 'PMI', '中国:火力发电:发电量(月)': '电力', '制造业pmi:从业人员': 'PMI', '聚酯:产能利用率:中国(周)': '化纤', '三聚氰胺:产量:中国(周)': '化学化工', '煤炭:样本洗煤厂(110家):开工率:中国(周)': '煤炭', '乙烯:石脑油裂解:产量:中国(周)': '化学化工', '重点电厂:日耗量:煤炭': '电力', '中国:产量:线材:主要钢厂': '钢铁', '硫酸钾:开工率:中国(周)': '化学化工', 'pta:产能利用率:中国(周)': '化纤', 'pe:社会库存:中国(周)': '橡胶塑料', '中国:产量:冷轧板卷:主要钢厂': '钢铁'}
2025-06-16 09:54:50.613291:    enable_hyperparameter_tuning: False
2025-06-16 09:54:50.613685:    enable_variable_selection: False
2025-06-16 09:54:50.614039:    enable_detailed_analysis: True
2025-06-16 09:54:50.614508:    generate_excel_report: True
2025-06-16 09:54:52.215218: ✅ 模型训练成功!
2025-06-16 09:54:52.215934: 生成的文件:
2025-06-16 09:54:52.216664:   ✅ final_model_joblib: test_outputs\final_dfm_model.joblib (29211 bytes)
2025-06-16 09:54:52.217209:   ✅ metadata: test_outputs\final_dfm_metadata.pkl (800 bytes)
2025-06-16 09:54:52.217595:   ✅ training_data: test_outputs\training_data.csv (19260 bytes)
2025-06-16 09:54:52.217996:   ✅ excel_report: test_outputs\comprehensive_dfm_report.xlsx (8005 bytes)
2025-06-16 09:54:52.218693: 
============================================================
2025-06-16 09:54:52.219022: 📊 测试结果总结
2025-06-16 09:54:52.219394: ============================================================
2025-06-16 09:54:52.219690: environment_setup: ✅ 成功
2025-06-16 09:54:52.220342: data_file_check: ✅ 成功
2025-06-16 09:54:52.220757: data_preparation: ✅ 成功
2025-06-16 09:54:52.221099: model_training: ✅ 成功
2025-06-16 09:54:52.221694: 
🎉 所有测试都成功完成!
2025-06-16 09:54:52.222114: ✅ DFM模型训练流程正常工作
2025-06-16 09:54:52.222838: 
📄 详细结果已保存到 test_results.txt
2025-06-16 09:54:52.223135: 📄 详细执行日志已保存到 test_execution_log.txt
