
# 示例测试脚本
import os
import sys
import pandas as pd
from datetime import date

# 设置环境
dashboard_path = os.path.join(os.getcwd(), 'dashboard')
sys.path.insert(0, dashboard_path)

# 导入模块
from DFM.data_prep.data_preparation import prepare_data
from DFM.train_model.tune_dfm import train_and_save_dfm_results

# 数据准备
excel_file = 'data/经济数据库0605.xlsx'
prepared_data, industry_map, transform_log, removed_vars_log = prepare_data(
    excel_path=excel_file,
    target_freq='W-FRI',
    target_sheet_name='规模以上工业增加值:当月同比',
    target_variable_name='规模以上工业增加值:当月同比',
    consecutive_nan_threshold=10,
    data_start_date='2020-01-01',
    data_end_date='2024-12-31',
    reference_sheet_name='指标体系',
    reference_column_name='高频指标'
)

# 检查数据
if prepared_data is not None and not prepared_data.empty:
    print(f"数据准备成功: {prepared_data.shape}")
    
    # 选择预测变量
    target_var = '规模以上工业增加值:当月同比'
    predictor_vars = [col for col in prepared_data.columns if col != target_var]
    
    if len(predictor_vars) > 0:
        # 选择前几个预测变量
        selected_indicators = predictor_vars[:5]
        
        # 模型训练
        results = train_and_save_dfm_results(
            input_df=prepared_data,
            target_variable=target_var,
            selected_indicators=selected_indicators,
            training_start_date=date(2020, 1, 3),
            validation_start_date=date(2024, 1, 5),
            validation_end_date=date(2024, 12, 31),
            n_factors=3,
            em_max_iter=50,
            output_base_dir="test_outputs",
            var_industry_map=industry_map,
            enable_detailed_analysis=True,
            generate_excel_report=True
        )
        
        if results:
            print("训练成功!")
            for file_type, file_path in results.items():
                print(f"{file_type}: {file_path}")
        else:
            print("训练失败")
    else:
        print("没有预测变量，无法训练")
else:
    print("数据准备失败")
