import streamlit as st
from datetime import datetime

# 导入配置
try:
    from config import (
        DataDefaults, TrainDefaults, UIDefaults, VisualizationDefaults,
        FileDefaults, FormatDefaults, AnalysisDefaults
    )
    CONFIG_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 无法导入配置模块: {e}")
    CONFIG_AVAILABLE = False

def render_dfm_data_prep_tab(st, session_state):
    """Renders the DFM Model Data Preparation tab."""
    st.markdown("#### 上传数据")

    # 初始化 session_state 中的文件存储
    if 'dfm_training_data_file' not in session_state:
        session_state.dfm_training_data_file = None
    
    # --- NEW: Initialize session_state for direct data passing ---
    if 'dfm_prepared_data_df' not in session_state:
        session_state.dfm_prepared_data_df = None
    if 'dfm_transform_log_obj' not in session_state:
        session_state.dfm_transform_log_obj = None
    if 'dfm_industry_map_obj' not in session_state:
        session_state.dfm_industry_map_obj = None
    if 'dfm_removed_vars_log_obj' not in session_state:
        session_state.dfm_removed_vars_log_obj = None
    if 'dfm_var_type_map_obj' not in session_state:
        session_state.dfm_var_type_map_obj = None
    # --- END NEW ---

    uploaded_file = st.file_uploader(
        "选择训练数据集 (例如：.csv, .xlsx)", 
        type=["csv", "xlsx"], 
        key="dfm_training_data_uploader",
        help="请上传包含模型训练所需指标的表格数据。"
    )

    if uploaded_file is not None:
        session_state.dfm_training_data_file = uploaded_file
        
        # 自动检测数据的日期范围并设置默认的结束日期
        try:
            import io
            import pandas as pd
            
            # 读取上传的文件以获取日期范围
            uploaded_file_bytes = uploaded_file.getvalue()
            excel_file_like_object = io.BytesIO(uploaded_file_bytes)
            
            # 尝试读取第一个sheet来获取日期信息
            if uploaded_file.name.endswith('.xlsx'):
                # 获取所有sheet名称
                try:
                    xl_file = pd.ExcelFile(excel_file_like_object)
                    sheet_names = xl_file.sheet_names
                    print(f"[日期检测] 发现 {len(sheet_names)} 个工作表: {sheet_names[:5]}...")  # 调试信息
                    
                    # 尝试从不同的sheet中找到日期列
                    date_info = None
                    sheets_checked = 0
                    all_date_ranges = []  # 收集所有工作表的日期范围
                    
                    for sheet_name in sheet_names:
                        # 跳过指标体系工作表
                        if sheet_name == '指标体系':
                            print(f"[日期检测] 跳过指标体系工作表")
                            continue
                            
                        sheets_checked += 1
                        if sheets_checked > 20:  # 增加限制到20个sheet
                            break
                            
                        try:
                            print(f"[日期检测] 正在检查工作表: {sheet_name}")
                            
                            # 使用detect_sheet_format来更好地处理不同格式
                            # 多种读取策略以确保能读取所有格式，特别关注最新数据
                            df_sample = None
                            successful_method = None
                            read_methods = [
                                {"name": "Wind格式", "skiprows": [0], "header": 0},
                                {"name": "标准格式", "skiprows": None, "header": 0}, 
                                {"name": "同花顺格式", "skiprows": [0, 2, 3, 4], "header": 0},
                                {"name": "跳过前2行", "skiprows": [0, 1], "header": 0}
                            ]
                            
                            for method in read_methods:
                                try:
                                    # 读取更多行以确保捕获最新数据
                                    df_sample = pd.read_excel(excel_file_like_object, sheet_name=sheet_name, 
                                                            nrows=20, header=method["header"], skiprows=method["skiprows"])
                                    if not df_sample.empty and len(df_sample.columns) > 0:
                                        print(f"[日期检测] 成功使用{method['name']}读取工作表 (形状: {df_sample.shape})")
                                        successful_method = method
                                        break
                                except Exception as e:
                                    print(f"[日期检测] {method['name']} 读取失败: {e}")
                                    continue
                            
                            if df_sample is None or df_sample.empty:
                                print(f"[日期检测] 所有读取方法都失败，跳过工作表 {sheet_name}")
                                continue
                            
                            if df_sample.empty or len(df_sample.columns) == 0:
                                print(f"[日期检测] 工作表 {sheet_name} 为空，跳过")
                                continue
                                
                            print(f"[日期检测] 工作表 {sheet_name} 列名: {list(df_sample.columns)[:3]}...")
                            
                            # 寻找可能的日期列 - 更加激进的检测策略
                            date_cols = []
                            
                            # 策略1: 通过列名识别
                            for col in df_sample.columns:
                                col_str = str(col).lower()
                                # 更广泛的日期列名检测
                                if any(keyword in col_str for keyword in ['date', 'time', '日期', '时间', '发布', '指标名称']):
                                    date_cols.append(col)
                                    print(f"[日期检测] 通过名称识别日期列: {col}")
                            
                            # 策略2: 如果没有通过名称找到，尝试内容检测所有列
                            if not date_cols and len(df_sample) > 0:
                                for col_idx, col in enumerate(df_sample.columns):
                                    try:
                                        sample_vals = df_sample[col].dropna().head(5)  # 增加样本数量
                                        if len(sample_vals) >= 2:  # 至少2个样本
                                            date_count = 0
                                            for val in sample_vals:
                                                if pd.notna(val):
                                                    try:
                                                        parsed_date = pd.to_datetime(val, errors='raise')
                                                        if 1990 <= parsed_date.year <= 2030:  # 合理的年份范围
                                                            date_count += 1
                                                    except:
                                                        pass
                                            
                                            # 如果大部分值都能转换为日期，则认为是日期列
                                            if date_count >= max(2, len(sample_vals) * 0.6):  # 至少60%的值是日期
                                                date_cols.append(col)
                                                print(f"[日期检测] 通过内容识别日期列: {col} (成功率: {date_count}/{len(sample_vals)})")
                                                break  # 找到一个就够了，通常第一个就是日期列
                                    except:
                                        continue
                            
                            if date_cols:
                                print(f"[日期检测] 在工作表 {sheet_name} 中找到 {len(date_cols)} 个日期列")
                                # 读取完整的数据以获取日期范围 - 使用相同的成功方法
                                try:
                                    # 使用刚才成功的方法读取完整数据
                                    successful_method = None
                                    for method in read_methods:
                                        try:
                                            df_test = pd.read_excel(excel_file_like_object, sheet_name=sheet_name, 
                                                                  nrows=5, header=method["header"], skiprows=method["skiprows"])
                                            if not df_test.empty and len(df_test.columns) > 0:
                                                successful_method = method
                                                break
                                        except:
                                            continue
                                    
                                    if successful_method:
                                        df_full = pd.read_excel(excel_file_like_object, sheet_name=sheet_name, 
                                                              header=successful_method["header"], skiprows=successful_method["skiprows"])
                                        print(f"[日期检测] 使用{successful_method['name']}读取完整数据")
                                    else:
                                        print(f"[日期检测] 无法读取完整数据，跳过")
                                        continue
                                    
                                    for date_col in date_cols:
                                        try:
                                            print(f"[日期检测] 正在分析日期列: {date_col}")
                                            date_series = pd.to_datetime(df_full[date_col], errors='coerce')
                                            date_series_clean = date_series.dropna()
                                            
                                            if len(date_series_clean) > 0:
                                                min_date = date_series_clean.min().date()
                                                max_date = date_series_clean.max().date()
                                                print(f"[日期检测] 日期范围: {min_date} 到 {max_date}")
                                                
                                                # 验证日期范围的合理性 - 放宽检查条件
                                                if (min_date.year >= 2000 and max_date.year <= 2030 and 
                                                    min_date <= max_date and 
                                                    len(date_series_clean) >= 5):  # 至少5个有效日期
                                                    # 收集这个工作表的日期范围和变量数量
                                                    variable_count = len(df_full.columns) - 1  # 减去日期列
                                                    all_date_ranges.append({
                                                        'sheet': sheet_name,
                                                        'min_date': min_date,
                                                        'max_date': max_date,
                                                        'count': len(date_series_clean),
                                                        'variable_count': max(0, variable_count)
                                                    })
                                                    print(f"[日期检测] 工作表 {sheet_name} 的日期范围已记录")
                                                    break
                                                else:
                                                    print(f"[日期检测] 日期范围不合理，跳过")
                                        except Exception as e_col:
                                            print(f"[日期检测] 处理日期列 {date_col} 时出错: {e_col}")
                                            continue
                                    
                                except Exception as e_full:
                                    print(f"[日期检测] 读取完整工作表 {sheet_name} 时出错: {e_full}")
                                    continue
                            else:
                                print(f"[日期检测] 工作表 {sheet_name} 中未找到日期列")
                                
                        except Exception as e_sheet:
                            print(f"[日期检测] 处理工作表 {sheet_name} 时出错: {e_sheet}")
                            continue
                    
                    # 如果找到了日期信息，计算整体范围
                    if all_date_ranges:
                        # 找到所有工作表中的最早和最晚日期
                        overall_min_date = min(r['min_date'] for r in all_date_ranges)
                        overall_max_date = max(r['max_date'] for r in all_date_ranges)
                        

                        
                        # 额外验证：确保我们得到了正确的最大日期
                        print(f"\n[日期检测] 验证计算的日期范围:")
                        print(f"  总体最小日期: {overall_min_date}")
                        print(f"  总体最大日期: {overall_max_date}")
                        print(f"  共处理了 {len(all_date_ranges)} 个工作表")
                        
                        # 找到数据最丰富的工作表作为主要来源
                        primary_source = max(all_date_ranges, key=lambda x: x['count'])
                        
                        # 找到最新数据的工作表
                        latest_source = max(all_date_ranges, key=lambda x: x['max_date'])
                        print(f"  最新数据来源: {latest_source['sheet']} (最新日期: {latest_source['max_date']})")
                        
                        date_info = (overall_min_date, overall_max_date, primary_source['sheet'], 'multiple_sources')
                        
                        # 设置数据结束日期为所有数据的最后一个观测值日期
                        session_state.dfm_param_data_end_date = overall_max_date
                        # 如果开始日期还没设置，也可以设置一个合理的开始日期
                        if session_state.dfm_param_data_start_date == datetime(2020, 1, 1).date():
                            session_state.dfm_param_data_start_date = overall_min_date
                        
                        st.success(f"文件 '{uploaded_file.name}' 已上传并准备就绪。")
                        
                        # 显示详细的日期范围信息 - 突出显示最新日期
                        range_info = f"📅 检测到数据整体日期范围：**{overall_min_date}** 至 **{overall_max_date}**"
                        st.info(range_info)
                        
                        # 显示解析统计信息
                        total_sheets_parsed = len(all_date_ranges)
                        total_variables = sum(r.get('variable_count', 0) for r in all_date_ranges)
                        st.info(f"📊 成功解析 **{total_sheets_parsed}** 个工作表，包含 **{total_variables}** 个数据变量")
                        
                        # 显示各工作表的日期范围详情
                        with st.expander("📊 各工作表日期范围详情", expanded=False):
                            sorted_ranges = sorted(all_date_ranges, key=lambda x: x['max_date'], reverse=True)
                            for i, r in enumerate(sorted_ranges):
                                emoji = "🥇" if i == 0 else "📊"  # 给最新的数据加标记
                                st.write(f"{emoji} **{r['sheet']}**: {r['min_date']} ~ {r['max_date']} ({r['count']} 个数据点)")
                            
                            # 统计各个月份的数据表数量
                            june_2025_count = sum(1 for r in all_date_ranges if r['max_date'].year == 2025 and r['max_date'].month == 6)
                            if june_2025_count > 0:
                                st.info(f"📈 共有 {june_2025_count} 个工作表包含2025年6月的数据")
                            
                            # 显示2025年6月的最大日期
                            june_dates = [r['max_date'] for r in all_date_ranges if r['max_date'].year == 2025 and r['max_date'].month == 6]
                            if june_dates:
                                max_june_date = max(june_dates)
                                st.info(f"🗓️ 2025年6月最新数据日期: **{max_june_date}**")
                    else:
                        st.success(f"文件 '{uploaded_file.name}' 已上传并准备就绪。")
                        st.warning("未能自动检测数据日期范围，请手动设置日期参数。")
                        # 提供一些调试信息
                        st.info(f"💡 检查了 {sheets_checked} 个工作表，建议手动检查数据格式或确认日期列名称。")
                        
                except Exception as e:
                    st.success(f"文件 '{uploaded_file.name}' 已上传并准备就绪。")
                    st.warning(f"自动检测日期范围时出错：{e}")
                    st.info("💡 建议手动设置日期参数。")
            else:
                # CSV文件处理
                try:
                    df_csv = pd.read_csv(excel_file_like_object, nrows=5)
                    # 类似的日期检测逻辑...
                    st.success(f"文件 '{uploaded_file.name}' 已上传并准备就绪。")
                except:
                    st.success(f"文件 '{uploaded_file.name}' 已上传并准备就绪。")
                    
        except Exception as e:
            st.success(f"文件 '{uploaded_file.name}' 已上传并准备就绪。")
            st.warning(f"自动检测数据信息时出错：{e}")
            st.info("💡 建议手动设置日期参数。")
            
    elif session_state.dfm_training_data_file is not None:
        st.info(f"当前已加载训练数据: {session_state.dfm_training_data_file.name}. 您可以上传新文件替换它。")
        
        # 添加文件结构检查工具
        with st.expander("🔍 文件结构诊断工具 (可选)", expanded=False):
            if st.button("检查文件结构", help="查看已上传文件的内部结构，帮助诊断格式问题"):
                try:
                    import io
                    import pandas as pd
                    
                    uploaded_file_bytes = session_state.dfm_training_data_file.getvalue()
                    excel_file_like_object = io.BytesIO(uploaded_file_bytes)
                    
                    if session_state.dfm_training_data_file.name.endswith('.xlsx'):
                        xl_file = pd.ExcelFile(excel_file_like_object)
                        sheet_names = xl_file.sheet_names
                        
                        st.write(f"**文件包含 {len(sheet_names)} 个工作表:**")
                        for i, sheet_name in enumerate(sheet_names):  # 显示所有工作表
                            with st.expander(f"工作表 {i+1}: {sheet_name}", expanded=(i==0)):
                                try:
                                    # 尝试使用格式检测
                                    try:
                                        from .data_preparation import detect_sheet_format
                                        format_info = detect_sheet_format(excel_file_like_object, sheet_name)
                                        st.write(f"**检测到的格式:** {format_info['format']} (来源: {format_info['source']})")
                                        st.write(f"**建议参数:** header={format_info['header']}, skiprows={format_info['skiprows']}")
                                    except:
                                        st.write("**格式检测:** 使用默认参数")
                                    
                                    # 读取前几行
                                    df_preview = pd.read_excel(excel_file_like_object, sheet_name=sheet_name, nrows=5)
                                    st.write(f"**数据形状:** {df_preview.shape}")
                                    st.write("**前5行预览:**")
                                    st.dataframe(df_preview)
                                    
                                    # 检查第一列是否可能是日期
                                    if len(df_preview.columns) > 0:
                                        first_col = df_preview.columns[0]
                                        first_col_values = df_preview[first_col].dropna().head(3)
                                        st.write(f"**第一列 '{first_col}' 的样本值:** {list(first_col_values)}")
                                        
                                        # 尝试转换为日期
                                        try:
                                            date_converted = pd.to_datetime(first_col_values, errors='coerce')
                                            if not date_converted.isna().all():
                                                st.success(f"✅ 第一列可以转换为日期: {date_converted.dropna().iloc[0]}")
                                            else:
                                                st.warning("⚠️ 第一列无法转换为日期")
                                        except:
                                            st.warning("⚠️ 第一列日期转换出错")
                                    
                                except Exception as e:
                                    st.error(f"读取工作表出错: {e}")
                        
                        # 移除限制显示的提示信息
                            
                except Exception as e:
                    st.error(f"文件结构检查出错: {e}")
    else:
        st.info("请上传训练数据集。")

    st.markdown("**说明:**")
    st.markdown("- 此处上传的数据将用于DFM模型的训练或重新训练。")
    st.markdown("- 请确保数据格式符合模型要求。")

    
    # 添加重要提示
    st.info("📌 **重要说明**: 下面设置的日期范围将作为系统处理数据的**最大边界**。后续的训练期、验证期设置必须在此范围内。结果展示的Nowcasting默认覆盖此完整时间范围。")

    param_defaults = {
        'dfm_param_target_variable': '规模以上工业增加值:当月同比',
        'dfm_param_target_sheet_name': '工业增加值同比增速_月度_同花顺',
        'dfm_param_target_freq': 'W-FRI',
        'dfm_param_remove_consecutive_nans': True,
        'dfm_param_consecutive_nan_threshold': 10,
        'dfm_param_type_mapping_sheet': DataDefaults.TYPE_MAPPING_SHEET if CONFIG_AVAILABLE else '指标体系',
        'dfm_param_data_start_date': datetime(2020, 1, 1).date(),
        'dfm_param_data_end_date': None
    }
    for key, default_value in param_defaults.items():
        if key not in session_state:
            session_state[key] = default_value

    row1_col1, row1_col2 = st.columns(2)
    with row1_col1:
        session_state.dfm_param_data_start_date = st.date_input(
            "数据开始日期 (系统边界)",
            value=session_state.dfm_param_data_start_date,
            key="ss_dfm_data_start",
            help="设置系统处理数据的最早日期边界。训练期、验证期必须在此日期之后。"
        )
    with row1_col2:
        session_state.dfm_param_data_end_date = st.date_input(
            "数据结束日期 (系统边界)",
            value=session_state.dfm_param_data_end_date,
            key="ss_dfm_data_end",
            help="设置系统处理数据的最晚日期边界。训练期、验证期必须在此日期之前。"
        )

    row2_col1, row2_col2 = st.columns(2)
    with row2_col1:
        session_state.dfm_param_target_sheet_name = st.text_input(
            "目标工作表名称 (Target Sheet Name)", 
            value=session_state.dfm_param_target_sheet_name,
            key="ss_dfm_target_sheet"
        )
    with row2_col2:
        session_state.dfm_param_target_variable = st.text_input(
            "目标变量 (Target Variable)", 
            value=session_state.dfm_param_target_variable,
            key="ss_dfm_target_var"
        )

    row3_col1, row3_col2 = st.columns(2)
    with row3_col1:
        session_state.dfm_param_consecutive_nan_threshold = st.number_input(
            "连续 NaN 阈值 (Consecutive NaN Threshold)", 
            min_value=0, 
            value=session_state.dfm_param_consecutive_nan_threshold, 
            step=1,
            key="ss_dfm_nan_thresh"
        )
    with row3_col2:
        session_state.dfm_param_remove_consecutive_nans = st.checkbox(
            "移除过多连续 NaN 的变量", 
            value=session_state.dfm_param_remove_consecutive_nans,
            key="ss_dfm_remove_nans",
            help="移除列中连续缺失值数量超过阈值的变量"
        )

    row4_col1, row4_col2 = st.columns(2)
    with row4_col1:
        session_state.dfm_param_target_freq = st.text_input(
            "目标频率 (Target Frequency)", 
            value=session_state.dfm_param_target_freq,
            help="例如: W-FRI, D, M, Q",
            key="ss_dfm_target_freq"
        )
    with row4_col2:
        session_state.dfm_param_type_mapping_sheet = st.text_input(
            "指标映射表名称 (Type Mapping Sheet)", 
            value=session_state.dfm_param_type_mapping_sheet,
            key="ss_dfm_type_map_sheet"
        )

    st.markdown("--- ") # Separator before the new section
    st.markdown("#### 数据预处理与导出")

    # Initialize session_state for new UI elements if not already present
    if 'dfm_export_base_name' not in session_state:
        session_state.dfm_export_base_name = "dfm_prepared_output"
    if 'dfm_processed_outputs' not in session_state: # For storing results to persist downloads
        session_state.dfm_processed_outputs = None

    left_col, right_col = st.columns([1, 2]) # Left col for inputs, Right col for outputs/messages

    with left_col:
        session_state.dfm_export_base_name = st.text_input(
            "导出文件基础名称 (Export Base Filename)",
            value=session_state.dfm_export_base_name,
            key="ss_dfm_export_basename"
        )

        run_button_clicked = st.button("运行数据预处理并导出", key="ss_dfm_run_preprocessing")

    with right_col:
        if run_button_clicked:
            session_state.dfm_processed_outputs = None # Clear previous downloadable results
            # --- NEW: Clear previous direct data objects --- 
            session_state.dfm_prepared_data_df = None
            session_state.dfm_transform_log_obj = None
            session_state.dfm_industry_map_obj = None
            session_state.dfm_removed_vars_log_obj = None
            session_state.dfm_var_type_map_obj = None
            # --- END NEW ---

            if session_state.dfm_training_data_file is None:
                st.error("错误：请先上传训练数据集！")
            elif not session_state.dfm_export_base_name:
                st.error("错误：请指定有效的文件基础名称！")
            else:
                try:
                    import io 
                    import json
                    import pandas as pd
                    from .data_preparation import prepare_data, load_mappings

                    st.info("正在进行数据预处理... 详细日志请查看运行Streamlit的控制台。")
                    with st.spinner("数据预处理正在进行中，请稍候..."):
                        uploaded_file_bytes = session_state.dfm_training_data_file.getvalue()
                        excel_file_like_object = io.BytesIO(uploaded_file_bytes)
                        
                        start_date_str = session_state.dfm_param_data_start_date.strftime('%Y-%m-%d') \
                            if session_state.dfm_param_data_start_date else None
                        end_date_str = session_state.dfm_param_data_end_date.strftime('%Y-%m-%d') \
                            if session_state.dfm_param_data_end_date else None
                        
                        # 🔧 修复：只有在启用移除连续NaN功能时才传递阈值
                        nan_threshold_int = None
                        if session_state.dfm_param_remove_consecutive_nans:
                            nan_threshold = session_state.dfm_param_consecutive_nan_threshold
                            if not pd.isna(nan_threshold):
                                try:
                                    nan_threshold_int = int(nan_threshold)
                                except ValueError:
                                    st.warning(f"连续NaN阈值 '{nan_threshold}' 不是一个有效的整数。将忽略此阈值。")
                                    nan_threshold_int = None

                        # 修复参数顺序以匹配data_preparation.py中的函数签名
                        results = prepare_data(
                            excel_path=excel_file_like_object,
                            target_freq=session_state.dfm_param_target_freq,
                            target_sheet_name=session_state.dfm_param_target_sheet_name,
                            target_variable_name=session_state.dfm_param_target_variable,
                            consecutive_nan_threshold=nan_threshold_int,
                            data_start_date=start_date_str,
                            data_end_date=end_date_str,
                            reference_sheet_name=session_state.dfm_param_type_mapping_sheet,
                            # reference_column_name 使用配置的默认值
                        )

                        if results:
                            # 修复解包顺序：prepare_data返回 (data, industry_map, transform_log, removed_vars_log)
                            prepared_data, industry_map, transform_log, removed_variables_detailed_log = results
                            
                            # --- NEW: Also load var_type_map separately using load_mappings ---
                            try:
                                var_type_map, var_industry_map_loaded = load_mappings(
                                    excel_path=excel_file_like_object,
                                    sheet_name=session_state.dfm_param_type_mapping_sheet,
                                    indicator_col=DataDefaults.INDICATOR_COLUMN if CONFIG_AVAILABLE else '高频指标',
                                    type_col=DataDefaults.TYPE_COLUMN if CONFIG_AVAILABLE else '类型',
                                    industry_col=DataDefaults.INDUSTRY_COLUMN if CONFIG_AVAILABLE else '行业'
                                )
                                # Store var_type_map separately in session_state
                                session_state.dfm_var_type_map_obj = var_type_map
                                st.info(f"✅ 已成功加载变量类型映射：{len(var_type_map)} 个映射")
                            except Exception as e_load_maps:
                                st.warning(f"加载变量类型映射失败: {e_load_maps}")
                                session_state.dfm_var_type_map_obj = {}
                            # --- END NEW ---
                            
                            # --- NEW: Store Python objects in session_state for direct use ---
                            session_state.dfm_prepared_data_df = prepared_data
                            session_state.dfm_transform_log_obj = transform_log
                            session_state.dfm_industry_map_obj = industry_map
                            session_state.dfm_removed_vars_log_obj = removed_variables_detailed_log
                            
                            st.success("数据预处理完成！结果已准备就绪，可用于模型训练模块。")
                            # --- END NEW ---

                            # Prepare for download (existing logic)
                            session_state.dfm_processed_outputs = {
                                'base_name': session_state.dfm_export_base_name,
                                'data': None, 'industry_map': None, 'transform_log': None, 'removed_vars_log': None
                            }
                            if prepared_data is not None:
                                session_state.dfm_processed_outputs['data'] = prepared_data.to_csv(index=True, index_label='Date', encoding='utf-8-sig').encode('utf-8-sig')
                            
                            if industry_map:
                                try:
                                    df_industry_map = pd.DataFrame(list(industry_map.items()), columns=['Indicator', 'Industry'])
                                    session_state.dfm_processed_outputs['industry_map'] = df_industry_map.to_csv(index=False, encoding='utf-8-sig').encode('utf-8-sig')
                                except Exception as e_im:
                                    st.warning(f"行业映射转换到CSV时出错: {e_im}")
                                    session_state.dfm_processed_outputs['industry_map'] = None
                            
                            if removed_variables_detailed_log:
                                try:
                                    df_removed_log = pd.DataFrame(removed_variables_detailed_log)
                                    session_state.dfm_processed_outputs['removed_vars_log'] = df_removed_log.to_csv(index=False, encoding='utf-8-sig').encode('utf-8-sig')
                                except Exception as e_rl:
                                    st.warning(f"移除变量日志转换到CSV时出错: {e_rl}")
                                    session_state.dfm_processed_outputs['removed_vars_log'] = None

                            # Handling transform_log (it's a dict, potentially nested)
                            if transform_log:
                                formatted_log_data = []
                                # Attempt to flatten or nicely format the transform_log for CSV
                                # This is a simplified example; actual flattening might be more complex
                                for category, entries in transform_log.items():
                                    if isinstance(entries, dict):
                                        for var, details in entries.items():
                                            if isinstance(details, dict):
                                                log_entry = {'Category': category, 'Variable': var}
                                                log_entry.update(details) # Add all sub-details
                                                formatted_log_data.append(log_entry)
                                    elif isinstance(entries, list): # e.g. for 'removed_highly_correlated_vars'
                                         for item_pair in entries:
                                            if isinstance(item_pair, (list, tuple)) and len(item_pair) == 2:
                                                formatted_log_data.append({'Category': category, 'Variable1': item_pair[0], 'Variable2': item_pair[1]})
                                            else:
                                                formatted_log_data.append({'Category': category, 'Detail': str(item_pair)})
                                
                                if formatted_log_data:
                                    try:
                                        df_transformed_log_nice = pd.DataFrame(formatted_log_data)
                                        session_state.dfm_processed_outputs['transform_log'] = df_transformed_log_nice.to_csv(index=False, encoding='utf-8-sig').encode('utf-8-sig')
                                    except Exception as e_tl:
                                        st.warning(f"转换日志到CSV时出错: {e_tl}. 将尝试保存为JSON字符串。")
                                        try:
                                            session_state.dfm_processed_outputs['transform_log'] = json.dumps(transform_log, ensure_ascii=False, indent=4).encode('utf-8-sig')
                                        except Exception as e_json:
                                            st.warning(f"转换日志到JSON时也出错: {e_json}")
                                            session_state.dfm_processed_outputs['transform_log'] = None
                                else:
                                    session_state.dfm_processed_outputs['transform_log'] = None 
                                    st.info("转换日志为空或格式无法直接转换为简单CSV。")
                            else:
                                session_state.dfm_processed_outputs['transform_log'] = None
                        
                        else:
                            st.error("数据预处理失败或未返回数据。请检查控制台日志获取更多信息。")
                            session_state.dfm_processed_outputs = None
                            # Ensure direct data objects are also None on failure
                            session_state.dfm_prepared_data_df = None
                            session_state.dfm_transform_log_obj = None
                            session_state.dfm_industry_map_obj = None
                            session_state.dfm_removed_vars_log_obj = None
                            session_state.dfm_var_type_map_obj = None
                
                except ImportError as ie:
                    st.error(f"导入错误: {ie}. 请确保 'data_preparation.py' 文件与UI脚本在同一目录下或正确安装。")
                except FileNotFoundError as fnfe:
                    st.error(f"文件未找到错误: {fnfe}. 这可能与 'data_preparation.py' 内部的文件读取有关。")
                except Exception as e:
                    st.error(f"运行数据预处理时发生错误: {e}")
                    import traceback
                    st.text_area("详细错误信息:", traceback.format_exc(), height=200)
                    session_state.dfm_processed_outputs = None

        # Render download buttons if data is available in session_state
        if session_state.dfm_processed_outputs:
            outputs = session_state.dfm_processed_outputs
            base_name = outputs['base_name']

            st.download_button(
                label=f"下载处理后的数据 ({base_name}_data_v3.csv)",
                data=outputs['data'],
                file_name=f"{base_name}_data_v3.csv",
                mime='text/csv',
                key='download_data_csv'
            )

            if outputs['industry_map']:
                st.download_button(
                    label=f"下载行业映射 ({base_name}_industry_map_v3.csv)",
                    data=outputs['industry_map'],
                    file_name=f"{base_name}_industry_map_v3.csv",
                    mime='text/csv',
                    key='download_industry_map_csv'
                )
            
            if outputs['transform_log']:
                st.download_button(
                    label=f"下载转换日志 ({base_name}_transform_log_v3.csv)",
                    data=outputs['transform_log'],
                    file_name=f"{base_name}_transform_log_v3.csv",
                    mime='text/csv',
                    key='download_transform_log_csv'
                )

            if outputs['removed_vars_log']:
                st.download_button(
                    label=f"下载移除变量日志 ({base_name}_removed_log_v3.csv)",
                    data=outputs['removed_vars_log'],
                    file_name=f"{base_name}_removed_log_v3.csv",
                    mime='text/csv',
                    key='download_removed_log_csv'
                )
