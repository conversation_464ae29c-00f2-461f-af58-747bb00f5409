#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的DFM测试脚本
"""

import os
import sys
import pandas as pd
from datetime import datetime

def main():
    print("🚀 开始简化测试")
    
    # 检查数据文件
    excel_file = 'data/经济数据库0605.xlsx'
    print(f"检查文件: {excel_file}")
    
    if not os.path.exists(excel_file):
        print(f"❌ 文件不存在: {excel_file}")
        return
    
    print(f"✅ 文件存在")
    
    # 读取Excel文件信息
    try:
        xl_file = pd.ExcelFile(excel_file)
        sheet_names = xl_file.sheet_names
        print(f"📊 工作表数量: {len(sheet_names)}")
        
        # 查找目标工作表
        target_sheet = '规模以上工业增加值:当月同比'
        if target_sheet in sheet_names:
            print(f"✅ 找到目标工作表: {target_sheet}")
            
            # 读取目标工作表的前几行
            df = pd.read_excel(excel_file, sheet_name=target_sheet, nrows=5)
            print(f"目标工作表形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            
        else:
            print(f"❌ 未找到目标工作表")
            print("可用工作表:")
            for i, name in enumerate(sheet_names[:10]):
                print(f"  {i+1}. {name}")
        
        # 检查指标体系工作表
        if '指标体系' in sheet_names:
            print(f"✅ 找到指标体系工作表")
            
            # 读取指标体系
            indicators_df = pd.read_excel(excel_file, sheet_name='指标体系', nrows=10)
            print(f"指标体系形状: {indicators_df.shape}")
            print(f"指标体系列名: {list(indicators_df.columns)}")
            
        else:
            print(f"❌ 未找到指标体系工作表")
            
    except Exception as e:
        print(f"❌ 读取Excel文件出错: {e}")
        return
    
    # 测试导入模块
    print(f"\n🔧 测试模块导入...")
    
    # 添加dashboard路径
    dashboard_path = os.path.join(os.getcwd(), 'dashboard')
    if dashboard_path not in sys.path:
        sys.path.insert(0, dashboard_path)
    
    try:
        from DFM.data_prep.data_preparation import prepare_data
        print("✅ 数据准备模块导入成功")
    except ImportError as e:
        print(f"❌ 数据准备模块导入失败: {e}")
        return
    
    try:
        from DFM.train_model.tune_dfm import train_and_save_dfm_results
        print("✅ 训练模块导入成功")
    except ImportError as e:
        print(f"❌ 训练模块导入失败: {e}")
        return
    
    print(f"\n🎉 基础测试完成!")
    print(f"✅ 文件存在且可读取")
    print(f"✅ 模块可以正常导入")
    print(f"✅ 准备进行完整测试")

if __name__ == "__main__":
    main()
