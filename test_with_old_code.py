#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用old文件夹中的代码进行测试
"""

import os
import sys
import pandas as pd
from datetime import datetime
import traceback

# 添加old文件夹到路径
old_path = os.path.join(os.getcwd(), 'old')
sys.path.insert(0, old_path)

def test_with_old_code():
    """使用old文件夹中的代码进行测试"""
    
    print("🚀 使用old文件夹代码进行测试")
    
    try:
        # 导入old文件夹中的模块
        from data_preparation import prepare_data, load_mappings
        from tune_dfm import run_tuning
        print("✅ old模块导入成功")
        
        # 检查数据文件
        excel_file = 'data/经济数据库0605.xlsx'
        if not os.path.exists(excel_file):
            print(f"❌ 数据文件不存在: {excel_file}")
            return False
        
        print(f"✅ 数据文件存在: {excel_file}")
        
        # 执行数据准备
        print("📊 开始数据准备...")
        
        prepared_data, industry_map, transform_log, removed_vars_log = prepare_data(
            excel_path=excel_file,
            target_freq='W-FRI',
            target_sheet_name='规模以上工业增加值:当月同比',
            target_variable_name='规模以上工业增加值:当月同比',
            consecutive_nan_threshold=10,
            data_start_date='2020-01-01',
            data_end_date='2024-12-31',
            reference_sheet_name='指标体系',
            reference_column_name='高频指标'
        )
        
        if prepared_data is not None and not prepared_data.empty:
            print(f"✅ 数据准备成功!")
            print(f"   数据形状: {prepared_data.shape}")
            print(f"   变量数量: {len(prepared_data.columns)}")
            
            # 保存数据
            prepared_data.to_csv('old_test_data.csv', encoding='utf-8-sig')
            print("✅ 数据已保存到 old_test_data.csv")
            
            # 检查是否有足够的预测变量
            target_var = '规模以上工业增加值:当月同比'
            predictor_vars = [col for col in prepared_data.columns if col != target_var]
            
            print(f"目标变量: {target_var}")
            print(f"预测变量数量: {len(predictor_vars)}")
            print(f"前10个预测变量: {predictor_vars[:10]}")
            
            if len(predictor_vars) > 0:
                print("✅ 有足够的预测变量进行DFM训练")
                
                # 尝试运行训练（如果old代码支持）
                try:
                    print("🤖 尝试运行DFM训练...")
                    result = run_tuning()
                    print(f"训练结果: {result}")
                except Exception as e:
                    print(f"⚠️ 训练过程出错: {e}")
                    
            else:
                print("❌ 没有预测变量，无法进行DFM训练")
                
        else:
            print("❌ 数据准备失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 DFM模型测试 - 使用old代码")
    print("=" * 60)
    
    success = test_with_old_code()
    
    if success:
        print("\n🎉 测试成功!")
        print("✅ 数据准备正常")
        print("✅ 模块导入正常")
        print("✅ 可以进行DFM训练")
    else:
        print("\n❌ 测试失败!")
        print("请检查错误信息")
    
    # 创建结果文件
    with open('test_result.txt', 'w', encoding='utf-8') as f:
        f.write(f"测试时间: {datetime.now()}\n")
        f.write(f"测试结果: {'成功' if success else '失败'}\n")
        f.write("详细信息请查看控制台输出\n")
    
    print(f"\n结果已保存到 test_result.txt")

if __name__ == "__main__":
    main()
