#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断DFM测试问题
"""

import os
import sys
import pandas as pd
import traceback

def diagnose():
    """诊断问题"""
    print("🔍 开始诊断...")
    
    # 1. 检查环境
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    
    # 2. 检查文件
    excel_file = 'data/经济数据库0605.xlsx'
    print(f"\n检查数据文件: {excel_file}")
    print(f"文件存在: {os.path.exists(excel_file)}")
    
    if os.path.exists(excel_file):
        try:
            xl_file = pd.ExcelFile(excel_file)
            sheet_names = xl_file.sheet_names
            print(f"工作表数量: {len(sheet_names)}")
            
            target_sheet = '规模以上工业增加值:当月同比'
            print(f"目标工作表存在: {target_sheet in sheet_names}")
            
            if target_sheet in sheet_names:
                # 尝试读取目标工作表
                df = pd.read_excel(excel_file, sheet_name=target_sheet, nrows=5)
                print(f"目标工作表可读取: True")
                print(f"目标工作表形状: {df.shape}")
                print(f"目标工作表列: {list(df.columns)}")
            
            indicators_sheet = '指标体系'
            print(f"指标体系工作表存在: {indicators_sheet in sheet_names}")
            
            if indicators_sheet in sheet_names:
                # 尝试读取指标体系
                df_indicators = pd.read_excel(excel_file, sheet_name=indicators_sheet, nrows=5)
                print(f"指标体系可读取: True")
                print(f"指标体系形状: {df_indicators.shape}")
                print(f"指标体系列: {list(df_indicators.columns)}")
                
        except Exception as e:
            print(f"读取Excel文件出错: {e}")
    
    # 3. 检查模块导入
    print(f"\n检查模块导入...")
    
    # 添加dashboard路径
    dashboard_path = os.path.join(os.getcwd(), 'dashboard')
    if dashboard_path not in sys.path:
        sys.path.insert(0, dashboard_path)
    print(f"添加路径: {dashboard_path}")
    
    try:
        from DFM.data_prep.data_preparation import prepare_data
        print("✅ data_preparation模块导入成功")
    except ImportError as e:
        print(f"❌ data_preparation模块导入失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
    
    try:
        from DFM.train_model.tune_dfm import train_and_save_dfm_results
        print("✅ tune_dfm模块导入成功")
    except ImportError as e:
        print(f"❌ tune_dfm模块导入失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
    
    # 4. 检查配置
    print(f"\n检查配置...")
    try:
        from config import DataDefaults
        print("✅ config模块导入成功")
        print(f"DataDefaults可用: {hasattr(DataDefaults, 'TARGET_FREQ')}")
    except ImportError as e:
        print(f"❌ config模块导入失败: {e}")
    
    # 5. 尝试简单的数据准备测试
    print(f"\n尝试简单的数据准备测试...")
    try:
        from DFM.data_prep.data_preparation import prepare_data
        
        # 最简单的参数
        result = prepare_data(
            excel_path=excel_file,
            target_freq='W-FRI',
            target_sheet_name='规模以上工业增加值:当月同比',
            target_variable_name='规模以上工业增加值:当月同比'
        )
        
        if result and len(result) == 4:
            prepared_data, industry_map, transform_log, removed_vars_log = result
            if prepared_data is not None:
                print(f"✅ 简单数据准备成功")
                print(f"数据形状: {prepared_data.shape}")
                print(f"变量数量: {len(prepared_data.columns)}")
            else:
                print(f"❌ 数据准备返回None")
        else:
            print(f"❌ 数据准备返回格式错误: {result}")
            
    except Exception as e:
        print(f"❌ 简单数据准备失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    diagnose()
    
    # 保存诊断结果
    with open('diagnosis_result.txt', 'w', encoding='utf-8') as f:
        f.write("诊断完成，请查看控制台输出\n")
    
    print(f"\n诊断完成！")
