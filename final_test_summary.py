#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DFM模型测试总结和解决方案
基于截图参数的完整测试流程
"""

import os
import sys
import pandas as pd
from datetime import datetime, date

def create_test_summary():
    """创建测试总结"""
    
    summary = """
DFM模型训练测试总结
==================

基于截图参数的测试配置:
- 目标变量: 规模以上工业增加值:当月同比
- 训练开始日期: 2020-01-03
- 验证开始日期: 2024-01-05  
- 验证结束日期: 2024-12-31
- 因子数量: 3
- EM最大迭代: 50
- 目标频率: W-FRI (周频率，周五)

测试发现的问题:
1. 数据准备阶段可能存在编码问题
2. 需要确保Excel文件中包含足够的预测变量
3. 模块导入路径需要正确设置

解决方案:
1. 确保数据文件 'data/经济数据库0605.xlsx' 存在且可读
2. 确保Excel文件包含以下工作表:
   - '规模以上工业增加值:当月同比' (目标变量工作表)
   - '指标体系' (变量映射工作表)
   - 其他经济指标工作表 (作为预测变量)

3. 运行完整的数据准备流程:
   - 使用dashboard/DFM/data_prep模块
   - 设置正确的参数
   - 确保生成包含多个变量的数据集

4. 运行模型训练:
   - 使用dashboard/DFM/train_model模块
   - 选择适当的预测变量
   - 设置训练和验证期参数

推荐的运行步骤:
1. 首先在Streamlit界面中运行数据准备
2. 确认准备好的数据包含多个变量
3. 在训练界面选择预测指标
4. 设置训练参数并运行训练

当前修复状态:
- ✅ 已修复训练模块中的变量检查逻辑
- ✅ 已添加详细的错误提示
- ✅ 已改进数据准备集成
- ✅ 已增强诊断信息

如果仍然遇到问题:
1. 检查Excel文件是否包含多个工作表的数据
2. 确认数据准备步骤正确完成
3. 验证选择了足够的预测变量
4. 查看详细的错误日志
"""
    
    return summary

def create_test_script():
    """创建可执行的测试脚本示例"""
    
    script = '''
# 示例测试脚本
import os
import sys
import pandas as pd
from datetime import date

# 设置环境
dashboard_path = os.path.join(os.getcwd(), 'dashboard')
sys.path.insert(0, dashboard_path)

# 导入模块
from DFM.data_prep.data_preparation import prepare_data
from DFM.train_model.tune_dfm import train_and_save_dfm_results

# 数据准备
excel_file = 'data/经济数据库0605.xlsx'
prepared_data, industry_map, transform_log, removed_vars_log = prepare_data(
    excel_path=excel_file,
    target_freq='W-FRI',
    target_sheet_name='规模以上工业增加值:当月同比',
    target_variable_name='规模以上工业增加值:当月同比',
    consecutive_nan_threshold=10,
    data_start_date='2020-01-01',
    data_end_date='2024-12-31',
    reference_sheet_name='指标体系',
    reference_column_name='高频指标'
)

# 检查数据
if prepared_data is not None and not prepared_data.empty:
    print(f"数据准备成功: {prepared_data.shape}")
    
    # 选择预测变量
    target_var = '规模以上工业增加值:当月同比'
    predictor_vars = [col for col in prepared_data.columns if col != target_var]
    
    if len(predictor_vars) > 0:
        # 选择前几个预测变量
        selected_indicators = predictor_vars[:5]
        
        # 模型训练
        results = train_and_save_dfm_results(
            input_df=prepared_data,
            target_variable=target_var,
            selected_indicators=selected_indicators,
            training_start_date=date(2020, 1, 3),
            validation_start_date=date(2024, 1, 5),
            validation_end_date=date(2024, 12, 31),
            n_factors=3,
            em_max_iter=50,
            output_base_dir="test_outputs",
            var_industry_map=industry_map,
            enable_detailed_analysis=True,
            generate_excel_report=True
        )
        
        if results:
            print("训练成功!")
            for file_type, file_path in results.items():
                print(f"{file_type}: {file_path}")
        else:
            print("训练失败")
    else:
        print("没有预测变量，无法训练")
else:
    print("数据准备失败")
'''
    
    return script

def main():
    """主函数"""
    print("创建DFM模型测试总结...")
    
    # 创建总结文件
    summary = create_test_summary()
    with open('DFM_TEST_SUMMARY.txt', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    # 创建示例脚本
    script = create_test_script()
    with open('example_test_script.py', 'w', encoding='utf-8') as f:
        f.write(script)
    
    print("文件已创建:")
    print("- DFM_TEST_SUMMARY.txt (测试总结)")
    print("- example_test_script.py (示例脚本)")
    
    # 检查当前状态
    print("\n当前状态检查:")
    
    # 检查数据文件
    excel_file = 'data/经济数据库0605.xlsx'
    if os.path.exists(excel_file):
        print(f"✅ 数据文件存在: {excel_file}")
        try:
            xl_file = pd.ExcelFile(excel_file)
            print(f"✅ Excel文件可读，包含 {len(xl_file.sheet_names)} 个工作表")
        except:
            print(f"❌ Excel文件读取失败")
    else:
        print(f"❌ 数据文件不存在: {excel_file}")
    
    # 检查模块
    dashboard_path = os.path.join(os.getcwd(), 'dashboard')
    if os.path.exists(dashboard_path):
        print(f"✅ Dashboard目录存在")
        
        data_prep_path = os.path.join(dashboard_path, 'DFM', 'data_prep', 'data_preparation.py')
        train_path = os.path.join(dashboard_path, 'DFM', 'train_model', 'tune_dfm.py')
        
        if os.path.exists(data_prep_path):
            print(f"✅ 数据准备模块存在")
        else:
            print(f"❌ 数据准备模块不存在")
            
        if os.path.exists(train_path):
            print(f"✅ 训练模块存在")
        else:
            print(f"❌ 训练模块不存在")
    else:
        print(f"❌ Dashboard目录不存在")
    
    print(f"\n总结完成! 请查看 DFM_TEST_SUMMARY.txt 了解详细信息。")

if __name__ == "__main__":
    main()
